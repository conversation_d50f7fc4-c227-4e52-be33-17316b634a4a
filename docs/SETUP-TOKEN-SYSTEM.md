# 🚀 Token Usage System Setup Guide

## ⚠️ **IMPORTANT: Database Setup Required**

The token usage system requires database tables that don't exist yet. Follow these steps to set up the system:

---

## 📋 **Step 1: Run Database Schema**

1. **Open your Supabase Dashboard**
   - Go to: https://supabase.com/dashboard/project/[your-project-id]
   - Navigate to **SQL Editor**

2. **Execute Main Schema**
   - Copy the entire content from `docs/database-updates/token-usage-policy-schema.sql`
   - Paste it into the SQL Editor
   - Click **"Run"** to execute

3. **Initialize User Tokens (Optional)**
   - Copy the content from `docs/database-updates/initialize-user-tokens.sql`
   - Paste it into the SQL Editor
   - Click **"Run"** to execute
   - This gives new users 10 $COPY tokens automatically

---

## 🔧 **Step 2: Environment Configuration**

Your `.env` file should already have these settings:

```env
TOKEN_USAGE_ENABLED=true
DEFAULT_TWEET_GENERATION_COST=0.5
DEFAULT_CONTEXT_FETCH_COST=0.05
# ... other costs
```

---

## 🧪 **Step 3: Test the System**

### **Test Database Setup:**
```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'buddychip_%';

-- Check token policies
SELECT * FROM buddychip_token_usage_policies;

-- Check your user tokens (replace with your user ID)
SELECT * FROM buddychip_copy_tokens WHERE user_id = 'your-user-id';
```

### **Test API Endpoints:**
```bash
# Get token policies
curl -X GET "http://localhost:3000/api/token-usage/policies"

# Check balance (replace with your wallet address)
curl -X GET "http://localhost:3000/api/copy-ai/balance?network=testnet&walletAddress=YOUR_WALLET"
```

### **Test UI:**
1. Go to `/copy-ai` (Copium page)
2. You should see cost displays: "Cost: 0.50 $COPY" for tweet generation
3. Try generating a tweet → balance should decrease
4. Try with insufficient balance → button should be disabled

---

## 🐛 **Troubleshooting**

### **Error: "relation buddychip_token_transactions does not exist"**
- ✅ **Solution**: Run the database schema from Step 1

### **Error: "next/headers" in client component**
- ✅ **Solution**: This is fixed in the latest code, restart your dev server

### **Error: No token policies loaded**
- Check if the API endpoint works: `curl http://localhost:3000/api/token-usage/policies`
- Check browser console for errors
- Verify database tables exist

### **Error: User has no token balance**
- Run the user initialization script from Step 1
- Or manually add tokens:
  ```sql
  SELECT add_user_tokens('your-user-id'::UUID, 100.0, 'Testing tokens');
  ```

---

## 📊 **Expected Results After Setup**

### **Database Tables Created:**
- ✅ `buddychip_token_usage_policies` - Action costs configuration
- ✅ `buddychip_copy_tokens` - User token balances
- ✅ `buddychip_token_transactions` - Transaction history

### **Default Token Costs:**
- ✅ Tweet Generation: 0.5 $COPY
- ✅ Context Fetch: 0.05 $COPY
- ✅ Tweet Analysis: 0.1 $COPY
- ✅ Image Generation: 1.0 $COPY
- ✅ And 6 more action types ready to use

### **UI Features:**
- ✅ Cost display before each action
- ✅ Button disable when insufficient balance
- ✅ Real-time balance updates
- ✅ Premium user bypass

---

## 🎯 **Quick Start Commands**

```bash
# 1. Start the development server
bun dev

# 2. Open Supabase Dashboard
# https://supabase.com/dashboard/project/[your-project-id]

# 3. Run the SQL scripts in this order:
# - docs/database-updates/token-usage-policy-schema.sql
# - docs/database-updates/initialize-user-tokens.sql

# 4. Test the system
# - Go to http://localhost:3000/copy-ai
# - Try generating a tweet
# - Check your token balance
```

---

## 🎉 **Success Indicators**

You'll know the system is working when:

1. ✅ **No database errors** in the console
2. ✅ **Cost displays** appear on buttons ("Cost: 0.50 $COPY")
3. ✅ **Token balance** shows in the dashboard
4. ✅ **Balance decreases** after actions
5. ✅ **Buttons disable** when balance is insufficient

---

## 📞 **Need Help?**

If you encounter issues:

1. **Check the console** for error messages
2. **Verify database tables** exist in Supabase
3. **Test API endpoints** directly
4. **Check environment variables** are set correctly
5. **Restart the development server** after database changes

The token usage system is now ready to create a sustainable economy for your $COPY token! 🚀
