import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import { withTokenUsage } from '@/lib/middleware/token-usage-middleware';
import { TokenAction } from '@/lib/web3/token-usage-types';

const contextLogger = logger.child({ component: 'CopyAIContext' });

// Request validation schema
const contextRequestSchema = z.object({
  input: z.string().min(1).max(500),
  type: z.enum(['twitter_post', 'twitter_handle']),
});

interface ContextData {
  type: 'twitter_post' | 'twitter_handle';
  content: string;
  metadata?: {
    author?: string;
    engagement?: {
      likes: number;
      retweets: number;
      replies: number;
    };
    created_at?: string;
  };
}

export async function POST(request: NextRequest) {
  const requestId = `context-${Date.now()}`;
  contextLogger.info('Context fetch request received', { requestId });

  try {
    // Parse and validate request body
    const body = await request.json();
    const { input, type } = contextRequestSchema.parse(body);

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      contextLogger.warn('Unauthorized context fetch attempt', { requestId });
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    contextLogger.info('Processing context fetch request', {
      userId: user.id,
      input: input.substring(0, 50) + '...',
      type,
      requestId
    });

    // Use token usage middleware
    const result = await withTokenUsage(
      {
        actionType: TokenAction.CONTEXT_FETCH,
        userId: user.id,
        metadata: {
          input: input.substring(0, 100),
          type,
          requestId,
        },
        description: `Context fetch for ${type}`,
      },
      async () => {
        let contextData: ContextData;

        if (type === 'twitter_post') {
          // Use Twitter API for specific post
          contextData = await fetchTwitterPost(input, requestId);
        } else {
          // Use xAI Live Search for handle-based context
          contextData = await fetchTwitterHandleContext(input, requestId);
        }

        return contextData;
      }
    );

    // Handle token usage result
    if (!result.success) {
      contextLogger.warn('Context fetch failed due to token usage', {
        userId: user.id,
        error: result.error,
        requestId
      });

      return NextResponse.json(
        { error: result.error },
        { status: 402 } // Payment Required
      );
    }

    const contextData = result.data!;

    contextLogger.info('Context fetched successfully', {
      userId: user.id,
      type,
      contentLength: contextData.content.length,
      costDeducted: result.costDeducted,
      requestId
    });

    return NextResponse.json({
      success: true,
      context: contextData,
      costDeducted: result.costDeducted,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      contextLogger.warn('Invalid request data', { error: error.errors, requestId });
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    contextLogger.error('Unexpected error in context fetch', { error, requestId });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Fetch specific Twitter post using Twitter API
 */
async function fetchTwitterPost(postUrl: string, requestId: string): Promise<ContextData> {
  contextLogger.info('Fetching Twitter post', { postUrl, requestId });

  // Extract tweet ID from URL
  const tweetIdMatch = postUrl.match(/status\/(\d+)/);
  if (!tweetIdMatch) {
    throw new Error('Invalid Twitter post URL');
  }

  const tweetId = tweetIdMatch[1];

  if (!process.env.TWITTER_BEARER_TOKEN) {
    contextLogger.error('Twitter API key not configured', { requestId });
    throw new Error('Twitter API not configured');
  }

  try {
    const response = await fetch(
      `https://api.twitter.com/2/tweets/${tweetId}?tweet.fields=created_at,public_metrics,author_id&expansions=author_id&user.fields=username`,
      {
        headers: {
          'Authorization': `Bearer ${process.env.TWITTER_BEARER_TOKEN}`
        }
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      contextLogger.error('Twitter API error', {
        status: response.status,
        error: errorData,
        requestId
      });
      throw new Error(`Twitter API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data.data) {
      throw new Error('Tweet not found or not accessible');
    }

    const tweet = data.data;
    const author = data.includes?.users?.[0];

    return {
      type: 'twitter_post',
      content: tweet.text,
      metadata: {
        author: author?.username,
        engagement: {
          likes: tweet.public_metrics?.like_count || 0,
          retweets: tweet.public_metrics?.retweet_count || 0,
          replies: tweet.public_metrics?.reply_count || 0,
        },
        created_at: tweet.created_at,
      },
    };

  } catch (error) {
    contextLogger.error('Error fetching Twitter post', { error, requestId });
    throw new Error('Failed to fetch Twitter post');
  }
}

/**
 * Fetch Twitter handle context using xAI Live Search
 */
async function fetchTwitterHandleContext(handle: string, requestId: string): Promise<ContextData> {
  contextLogger.info('Fetching Twitter handle context', { handle, requestId });

  // Clean handle (remove @ if present)
  const cleanHandle = handle.replace(/^@/, '');

  if (!process.env.XAI_API_KEY) {
    contextLogger.error('xAI API key not configured', { requestId });
    throw new Error('xAI API not configured');
  }

  try {
    const response = await fetch('https://api.x.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.XAI_API_KEY}`
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: `Provide a summary of the recent tweets and posting style of @${cleanHandle}. Include their typical topics, tone, and recent viral content.`
          }
        ],
        search_parameters: {
          mode: 'on',
          sources: [{ type: 'x', x_handles: [cleanHandle] }],
          max_search_results: 10
        },
        model: 'grok-3-latest'
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      contextLogger.error('xAI API error', {
        status: response.status,
        error: errorData,
        requestId
      });
      throw new Error(`xAI API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data.choices?.[0]?.message?.content) {
      throw new Error('No content returned from xAI');
    }

    return {
      type: 'twitter_handle',
      content: data.choices[0].message.content,
      metadata: {
        author: cleanHandle,
      },
    };

  } catch (error) {
    contextLogger.error('Error fetching Twitter handle context', { error, requestId });
    throw new Error('Failed to fetch Twitter handle context');
  }
}
