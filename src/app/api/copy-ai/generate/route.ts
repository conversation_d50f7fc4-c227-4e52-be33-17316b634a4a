import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { generateViralTweet } from '@/lib/ai/viral-tweet-generator';
import {
  getUserTokenBalance,
  hasPremiumAccess,
} from '@/lib/web3/token-service';
import { withTokenUsage } from '@/lib/middleware/token-usage-middleware';
import { TokenAction } from '@/lib/web3/token-usage-types';
// Copy.AI pricing configuration (moved from web3/config to avoid client-side imports in API routes)
const COPY_CONFIG = {
  GENERATION_COST: parseFloat(process.env.COPY_GENERATION_COST || '0.5'),
  THEMES: {
    MEME: 'meme',
    DEGEN: 'degen',
    VC_BAIT: 'vc_bait',
    GENERAL: 'general',
  },
} as const;
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';

const generateLogger = logger.child({ component: 'CopyAIGenerate' });

// Request validation schema
const generateRequestSchema = z.object({
  prompt: z.string().min(1).max(500),
  theme: z.enum(['general', 'meme', 'degen', 'vc_bait']),
  context: z.object({
    type: z.enum(['twitter_post', 'twitter_handle']),
    content: z.string(),
    metadata: z.object({
      author: z.string().optional(),
      engagement: z.object({
        likes: z.number(),
        retweets: z.number(),
        replies: z.number(),
      }).optional(),
      created_at: z.string().optional(),
    }).optional(),
  }).optional(),
});

export async function POST(request: NextRequest) {
  const requestId = `generate-${Date.now()}`;
  generateLogger.info('Tweet generation request received', { requestId });

  try {
    // Parse and validate request body
    const body = await request.json();
    const { prompt, theme, context } = generateRequestSchema.parse(body);

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      generateLogger.warn('Unauthorized generation attempt', { requestId });
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    generateLogger.info('Processing generation request', {
      userId: user.id,
      prompt: prompt.substring(0, 50) + '...',
      theme,
      hasContext: !!context,
      contextType: context?.type,
      requestId
    });

    // Check if user has premium access (unlimited generations)
    const hasPremium = await hasPremiumAccess(user.id);

    // Use token usage middleware for non-premium users
    const result = await withTokenUsage(
      {
        actionType: TokenAction.TWEET_GENERATION,
        userId: user.id,
        metadata: {
          prompt: prompt.substring(0, 100),
          theme,
          hasContext: !!context,
          contextType: context?.type,
          requestId,
        },
        skipIfDisabled: hasPremium, // Skip token usage for premium users
      },
      async () => {
        // Generate the viral tweet
        const generatedTweet = await generateViralTweet(prompt, theme, requestId, context);

        // Store the generated tweet in database
        const { data: tweetRecord, error: insertError } = await supabase
          .from('buddychip_generated_tweets')
          .insert({
            user_id: user.id,
            prompt,
            generated_content: generatedTweet.content,
            theme,
            cost_paid: hasPremium ? 0 : COPY_CONFIG.GENERATION_COST,
            viral_score: generatedTweet.viralScore,
          })
          .select()
          .single();

        if (insertError) {
          generateLogger.error('Error storing generated tweet', {
            userId: user.id,
            insertError,
            requestId
          });
          throw new Error('Failed to store generated tweet');
        }

        return {
          generatedTweet,
          tweetRecord,
        };
      }
    );

    // Handle token usage result
    if (!result.success) {
      generateLogger.warn('Tweet generation failed due to token usage', {
        userId: user.id,
        error: result.error,
        requestId
      });

      return NextResponse.json(
        { error: result.error },
        { status: 402 } // Payment Required
      );
    }

    const { generatedTweet, tweetRecord } = result.data!;

    // Get updated balance
    const updatedBalance = await getUserTokenBalance(user.id);

    generateLogger.info('Tweet generation completed successfully', {
      userId: user.id,
      tweetId: tweetRecord.id,
      viralScore: generatedTweet.viralScore,
      newBalance: updatedBalance.balance,
      requestId
    });

    return NextResponse.json({
      success: true,
      tweet: {
        id: tweetRecord.id,
        content: generatedTweet.content,
        theme: generatedTweet.theme,
        viralScore: generatedTweet.viralScore,
        hashtags: generatedTweet.hashtags,
        estimatedEngagement: generatedTweet.estimatedEngagement,
        costPaid: hasPremium ? 0 : result.costDeducted,
        createdAt: tweetRecord.created_at,
      },
      balance: updatedBalance,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      generateLogger.warn('Invalid request data', { error: error.errors, requestId });
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    generateLogger.error('Unexpected error in tweet generation', { error, requestId });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user's generated tweets
    const { data: tweets, error: tweetsError } = await supabase
      .from('buddychip_generated_tweets')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(20);

    if (tweetsError) {
      generateLogger.error('Error fetching user tweets', {
        userId: user.id,
        tweetsError
      });
      return NextResponse.json(
        { error: 'Failed to fetch tweets' },
        { status: 500 }
      );
    }

    // Get user balance
    const balance = await getUserTokenBalance(user.id);
    const hasPremium = await hasPremiumAccess(user.id);

    return NextResponse.json({
      tweets: tweets || [],
      balance,
      hasPremiumAccess: hasPremium,
      generationCost: COPY_CONFIG.GENERATION_COST,
    });

  } catch (error) {
    generateLogger.error('Error in GET /api/copy-ai/generate', { error });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
