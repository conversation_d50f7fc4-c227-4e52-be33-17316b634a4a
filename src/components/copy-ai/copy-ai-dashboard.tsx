'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { ImageGenerationButton } from '@/components/ui/image-generation-button';
import { Co<PERSON>, Sparkles, TrendingUp, Copy, ImageIcon, Link, Search, Loader2, RefreshCw } from 'lucide-react';
import { useNetwork } from '@/contexts/network-context';
import { useWallet } from '@solana/wallet-adapter-react';
import { useTokenUsage } from '@/hooks/useTokenUsage';
import { ActionCostBadge, ActionCostDisplay } from '@/components/ui/action-cost-badge';
import { TokenAction } from '@/lib/web3/token-usage-service';

interface GeneratedTweet {
  id: string;
  content: string;
  theme: string;
  viralScore: number;
  hashtags: string[];
  estimatedEngagement: {
    likes: number;
    retweets: number;
    replies: number;
  };
  costPaid: number;
  createdAt: string;
}

interface TokenBalance {
  balance: number;
  stakedAmount: number;
  totalEarned: number;
  totalSpent: number;
}

interface OnChainBalance {
  solBalance: number;
  copyTokenBalance: number;
}

interface BalanceData {
  database: TokenBalance;
  onChain: OnChainBalance | null;
  network: string;
  walletAddress: string | null;
}

interface ContextData {
  type: 'twitter_post' | 'twitter_handle' | null;
  content: string;
  metadata?: {
    author?: string;
    engagement?: {
      likes: number;
      retweets: number;
      replies: number;
    };
    created_at?: string;
  };
}

const themes = [
  { id: 'general', name: 'General Viral', icon: '🚀', description: 'Broadly appealing content' },
  { id: 'meme', name: 'Meme Mode', icon: '😂', description: 'Humorous internet culture' },
  { id: 'degen', name: 'Degen Mode', icon: '💎', description: 'Crypto/DeFi focused' },
  { id: 'vc_bait', name: 'VC Bait', icon: '💼', description: 'Professional thought-leadership' },
];

export function CopyAIDashboard() {
  // Hooks
  const { currentNetwork } = useNetwork();
  const { publicKey, connected } = useWallet();
  const { getActionCost, canAffordAction } = useTokenUsage();

  // State
  const [prompt, setPrompt] = useState('');
  const [selectedTheme, setSelectedTheme] = useState('general');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedTweets, setGeneratedTweets] = useState<GeneratedTweet[]>([]);
  const [balance, setBalance] = useState<TokenBalance | null>(null);
  const [onChainBalance, setOnChainBalance] = useState<OnChainBalance | null>(null);
  const [hasPremiumAccess, setHasPremiumAccess] = useState(false);
  const [generationCost, setGenerationCost] = useState(0.5);
  const [isLoadingBalance, setIsLoadingBalance] = useState(false);

  // Context-related state
  const [contextInput, setContextInput] = useState('');
  const [contextData, setContextData] = useState<ContextData | null>(null);
  const [isLoadingContext, setIsLoadingContext] = useState(false);

  // Load user data on component mount and when network/wallet changes
  useEffect(() => {
    loadUserData();
  }, []);

  useEffect(() => {
    console.log('🔄 Wallet/Network effect triggered:', {
      publicKey: publicKey?.toString(),
      connected,
      currentNetwork,
    });

    if (publicKey && connected) {
      loadBalanceData();
    }
  }, [currentNetwork, publicKey, connected]);

  const loadUserData = async () => {
    try {
      console.log('🔄 Loading user data...');
      const response = await fetch('/api/copy-ai/generate');
      if (response.ok) {
        const data = await response.json();
        setGeneratedTweets(data.tweets);
        setBalance(data.balance);
        setHasPremiumAccess(data.hasPremiumAccess);
        setGenerationCost(data.generationCost);
        console.log('✅ User data loaded:', data);
      }
    } catch (error: unknown) {
      console.error('❌ Error loading user data:', error instanceof Error ? error.message : 'Unknown error');
      toast.error('Failed to load user data');
    }
  };

  const loadBalanceData = async () => {
    if (!publicKey || !connected) {
      console.log('❌ Cannot load balance data - wallet not connected:', {
        publicKey: publicKey?.toString(),
        connected,
      });
      return;
    }

    setIsLoadingBalance(true);
    try {
      console.log('🪙 Loading balance data for network:', currentNetwork, 'wallet:', publicKey.toString());

      const params = new URLSearchParams({
        network: currentNetwork,
        walletAddress: publicKey.toString(),
      });

      const response = await fetch(`/api/copy-ai/balance?${params}`);
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Balance data loaded:', data);
        setOnChainBalance(data.onChain);

        // Show toast with balance info
        if (data.onChain) {
          toast.success(`On-chain balance: ${data.onChain.copyTokenBalance} $COPY on ${currentNetwork}`);
        } else {
          toast.info(`No on-chain $COPY tokens found on ${currentNetwork}`);
        }
      } else {
        console.error('❌ Failed to load balance data');
      }
    } catch (error) {
      console.error('❌ Error loading balance data:', error);
      toast.error('Failed to load on-chain balance');
    } finally {
      setIsLoadingBalance(false);
    }
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error('Please enter a prompt');
      return;
    }

    if (!hasPremiumAccess && balance && balance.balance < generationCost) {
      toast.error(`Insufficient $COPY balance. Need ${generationCost} $COPY`);
      return;
    }

    setIsGenerating(true);

    try {
      console.log('🚀 Generating tweet with context:', contextData ? 'Yes' : 'No');

      const response = await fetch('/api/copy-ai/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          theme: selectedTheme,
          context: contextData,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setGeneratedTweets(prev => [data.tweet, ...prev]);
        setBalance(data.balance);
        setPrompt('');
        toast.success('Viral tweet generated successfully!');
      } else {
        toast.error(data.error || 'Failed to generate tweet');
      }
    } catch (error) {
      console.error('Error generating tweet:', error);
      toast.error('Failed to generate tweet');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success('Tweet copied to clipboard!');
  };

  // Context fetching functions
  const detectInputType = (input: string): 'twitter_post' | 'twitter_handle' | null => {
    console.log('🔍 Detecting input type for:', input);

    // Check if it's a Twitter post URL
    const twitterPostRegex = /(?:https?:\/\/)?(?:www\.)?(?:twitter\.com|x\.com)\/\w+\/status\/\d+/i;
    if (twitterPostRegex.test(input)) {
      console.log('✅ Detected Twitter post URL');
      return 'twitter_post';
    }

    // Check if it's a Twitter handle (with or without @)
    const handleRegex = /^@?[a-zA-Z0-9_]{1,15}$/;
    if (handleRegex.test(input.trim())) {
      console.log('✅ Detected Twitter handle');
      return 'twitter_handle';
    }

    console.log('❌ Input type not recognized');
    return null;
  };

  const fetchContext = async () => {
    if (!contextInput.trim()) {
      toast.error('Please enter a Twitter URL or handle');
      return;
    }

    const inputType = detectInputType(contextInput.trim());
    if (!inputType) {
      toast.error('Please enter a valid Twitter post URL or handle (e.g., @username or https://twitter.com/user/status/123)');
      return;
    }

    setIsLoadingContext(true);
    console.log('🚀 Fetching context for:', contextInput, 'Type:', inputType);

    try {
      const response = await fetch('/api/copy-ai/context', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: contextInput.trim(),
          type: inputType,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setContextData(data.context);
        console.log('✅ Context fetched successfully:', data.context);
        toast.success(`Context loaded from ${inputType === 'twitter_post' ? 'Twitter post' : 'Twitter handle'}`);
      } else {
        console.error('❌ Error fetching context:', data.error);
        toast.error(data.error || 'Failed to fetch context');
      }
    } catch (error) {
      console.error('❌ Error fetching context:', error);
      toast.error('Failed to fetch context');
    } finally {
      setIsLoadingContext(false);
    }
  };

  const clearContext = () => {
    console.log('🧹 Clearing context data');
    setContextData(null);
    setContextInput('');
    toast.success('Context cleared');
  };

  const getViralScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Sparkles className="h-8 w-8 text-purple-500" />
          Copium On-Chain
        </h1>
        <p className="text-muted-foreground">
          Turn prompts into viral tweets with AI • Powered by $COPY tokens
        </p>
      </div>

      {/* Balance Card */}
      {balance && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Coins className="h-5 w-5" />
                Token Balance
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {currentNetwork}
                </Badge>
                {publicKey && connected && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadBalanceData}
                    disabled={isLoadingBalance}
                    className="h-8 px-2"
                  >
                    {isLoadingBalance ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <RefreshCw className="h-3 w-3" />
                    )}
                  </Button>
                )}
              </div>
            </CardTitle>
            <CardDescription>
              Database balance (for generation costs) and on-chain balance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Database Balance */}
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-3">Database Balance (Generation Credits)</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Available</p>
                    <p className="text-2xl font-bold">{balance.balance.toFixed(2)} $COPY</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Staked</p>
                    <p className="text-2xl font-bold">{balance.stakedAmount.toFixed(2)} $COPY</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Earned</p>
                    <p className="text-2xl font-bold text-green-600">{balance.totalEarned.toFixed(2)} $COPY</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Total Spent</p>
                    <p className="text-2xl font-bold text-red-600">{balance.totalSpent.toFixed(2)} $COPY</p>
                  </div>
                </div>
              </div>

              {/* On-Chain Balance */}
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-3">
                  On-Chain Balance ({currentNetwork})
                </h4>
                {publicKey && connected ? (
                  onChainBalance ? (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">$COPY Tokens</p>
                        <p className="text-2xl font-bold">{onChainBalance.copyTokenBalance.toFixed(2)} $COPY</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">SOL Balance</p>
                        <p className="text-2xl font-bold">{onChainBalance.solBalance.toFixed(4)} SOL</p>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-muted-foreground">
                        {isLoadingBalance ? 'Loading on-chain balance...' : 'No tokens found on this network'}
                      </p>
                    </div>
                  )
                ) : (
                  <div className="text-center py-4">
                    <p className="text-muted-foreground">
                      Connect wallet to view on-chain balance
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Wallet connected: {connected ? 'Yes' : 'No'} | PublicKey: {publicKey ? 'Yes' : 'No'}
                    </p>
                    {publicKey && !connected && (
                      <p className="text-xs text-orange-500 mt-1">
                        Wallet detected but not connected properly
                      </p>
                    )}
                    {publicKey && connected && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={loadBalanceData}
                        className="mt-2"
                      >
                        Retry Loading Balance
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div>

            {hasPremiumAccess && (
              <Badge variant="secondary" className="mt-4">
                Premium Access Active - Unlimited Generations
              </Badge>
            )}
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="generate" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="generate">Generate Tweet</TabsTrigger>
          <TabsTrigger value="history">My Tweets</TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-6">
          {/* Generation Form */}
          <Card>
            <CardHeader>
              <CardTitle>Generate Viral Tweet</CardTitle>
              <CardDescription>
                Enter a prompt and select a theme to generate a viral tweet
                {!hasPremiumAccess && ` (Cost: ${generationCost} $COPY)`}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Context Input Section */}
              <div className="space-y-3">
                <Label htmlFor="context">Context (Optional)</Label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      id="context"
                      placeholder="Enter Twitter post URL or handle (e.g., @username or https://twitter.com/user/status/123)"
                      value={contextInput}
                      onChange={(e) => setContextInput(e.target.value)}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={fetchContext}
                      disabled={isLoadingContext || !contextInput.trim() || (balance ? !canAffordAction(TokenAction.CONTEXT_FETCH, balance.balance) : false)}
                      className="px-3"
                    >
                      {isLoadingContext ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Search className="h-4 w-4" />
                      )}
                    </Button>
                    {contextData && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={clearContext}
                        className="px-3"
                      >
                        ✕
                      </Button>
                    )}
                  </div>

                  {balance && contextInput.trim() && (
                    <ActionCostDisplay
                      actionType={TokenAction.CONTEXT_FETCH}
                      userBalance={balance.balance}
                      className="text-xs"
                    />
                  )}
                </div>

                {/* Context Display */}
                {contextData && (
                  <Alert>
                    <Link className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">
                            {contextData.type === 'twitter_post' ? '📄 Post' : '👤 Handle'}
                          </Badge>
                          {contextData.metadata?.author && (
                            <span className="text-sm font-medium">@{contextData.metadata.author}</span>
                          )}
                        </div>
                        <p className="text-sm bg-muted p-2 rounded max-h-20 overflow-y-auto">
                          {contextData.content.length > 200
                            ? `${contextData.content.substring(0, 200)}...`
                            : contextData.content}
                        </p>
                        {contextData.metadata?.engagement && (
                          <div className="flex gap-4 text-xs text-muted-foreground">
                            <span>❤️ {contextData.metadata.engagement.likes}</span>
                            <span>🔄 {contextData.metadata.engagement.retweets}</span>
                            <span>💬 {contextData.metadata.engagement.replies}</span>
                          </div>
                        )}
                      </div>
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              <div>
                <Label htmlFor="prompt">Prompt</Label>
                <Textarea
                  id="prompt"
                  placeholder="e.g., 'memecoin hype', 'AI breakthrough', 'startup advice'..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="mt-1"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="theme">Theme</Label>
                <Select value={selectedTheme} onValueChange={setSelectedTheme}>
                  <SelectTrigger className="mt-1 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600">
                    <SelectValue placeholder="Select a theme">
                      <div className="flex items-center gap-2">
                        <span>{themes.find(t => t.id === selectedTheme)?.icon}</span>
                        <span className="text-gray-900 dark:text-gray-100">{themes.find(t => t.id === selectedTheme)?.name}</span>
                      </div>
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-lg">
                    {themes.map((theme) => (
                      <SelectItem
                        key={theme.id}
                        value={theme.id}
                        className="focus:bg-blue-100 dark:focus:bg-blue-900 hover:bg-blue-50 dark:hover:bg-blue-900/50 cursor-pointer data-[highlighted]:bg-blue-100 dark:data-[highlighted]:bg-blue-900 transition-colors"
                      >
                        <div className="flex items-center gap-3 py-2 w-full">
                          <span className="text-lg flex-shrink-0">{theme.icon}</span>
                          <div className="flex flex-col min-w-0 flex-1">
                            <div className="font-medium text-gray-900 dark:text-gray-100 text-sm">{theme.name}</div>
                            <div className="text-xs text-gray-600 dark:text-gray-300 leading-tight">{theme.description}</div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={handleGenerate}
                  disabled={isGenerating || (!hasPremiumAccess && !!balance && !canAffordAction(TokenAction.TWEET_GENERATION, balance.balance))}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate Viral Tweet
                    </>
                  )}
                </Button>

                {balance && !hasPremiumAccess && (
                  <ActionCostDisplay
                    actionType={TokenAction.TWEET_GENERATION}
                    userBalance={balance.balance}
                    className="justify-center text-center"
                  />
                )}

                {hasPremiumAccess && (
                  <div className="text-center text-sm text-green-600">
                    <Sparkles className="w-4 h-4 inline mr-1" />
                    Premium: Unlimited generations
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          {/* Generated Tweets History */}
          <div className="space-y-4">
            {generatedTweets.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <p className="text-muted-foreground">No tweets generated yet</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Generate your first viral tweet to get started!
                  </p>
                </CardContent>
              </Card>
            ) : (
              generatedTweets.map((tweet) => (
                <Card key={tweet.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {themes.find(t => t.id === tweet.theme)?.icon} {themes.find(t => t.id === tweet.theme)?.name}
                        </Badge>
                        <Badge variant="secondary" className={getViralScoreColor(tweet.viralScore)}>
                          <TrendingUp className="h-3 w-3 mr-1" />
                          {tweet.viralScore}/100
                        </Badge>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(tweet.content)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <ImageGenerationButton
                          contextType="copy-ai"
                          contextContent={tweet.content}
                          defaultPrompt={`Create a viral social media image for: ${tweet.content.substring(0, 100)}${tweet.content.length > 100 ? '...' : ''}`}
                          variant="outline"
                          size="sm"
                        >
                          <ImageIcon className="h-4 w-4" />
                        </ImageGenerationButton>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="p-4 bg-muted rounded-lg">
                        <p className="whitespace-pre-wrap">{tweet.content}</p>
                      </div>

                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Est. Likes</p>
                          <p className="font-medium">{tweet.estimatedEngagement.likes}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Est. Retweets</p>
                          <p className="font-medium">{tweet.estimatedEngagement.retweets}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Est. Replies</p>
                          <p className="font-medium">{tweet.estimatedEngagement.replies}</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <span>Cost: {tweet.costPaid} $COPY</span>
                        <span>{new Date(tweet.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
