import { TokenUsageService } from '@/lib/web3/token-usage-service';
import { TokenAction, UsageTransaction } from '@/lib/web3/token-usage-types';
import { logger } from '@/lib/utils/logger';

const middlewareLogger = logger.child({ component: 'TokenUsageMiddleware' });

/**
 * Configuration for token usage middleware
 */
export interface TokenUsageConfig {
  actionType: TokenAction;
  userId: string;
  metadata?: Record<string, any>;
  relatedEntityId?: string;
  description?: string;
  skipIfDisabled?: boolean;
}

/**
 * Result of token usage execution
 */
export interface TokenUsageResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  costDeducted: number;
  balanceAfter?: number;
}

/**
 * Middleware to handle token usage for actions
 * This ensures atomic operations: check balance -> execute action -> deduct tokens
 */
export async function withTokenUsage<T>(
  config: TokenUsageConfig,
  handler: () => Promise<T>
): Promise<TokenUsageResult<T>> {
  const { actionType, userId, metadata, relatedEntityId, description, skipIfDisabled } = config;

  middlewareLogger.info('Starting token usage middleware', {
    actionType,
    userId,
    metadata,
  });

  try {
    // Check if token usage is enabled
    const isEnabled = process.env.TOKEN_USAGE_ENABLED === 'true';
    if (!isEnabled && skipIfDisabled) {
      middlewareLogger.info('Token usage disabled, executing without cost', { actionType, userId });
      const data = await handler();
      return {
        success: true,
        data,
        costDeducted: 0,
      };
    }

    // Step 1: Get action cost
    const actionCost = await TokenUsageService.getActionCost(actionType);

    if (actionCost === 0) {
      middlewareLogger.info('Action has no cost, executing freely', { actionType, userId });
      const data = await handler();
      return {
        success: true,
        data,
        costDeducted: 0,
      };
    }

    // Step 2: Check if user can afford the action
    const canAfford = await TokenUsageService.canAffordAction(userId, actionType);

    if (!canAfford) {
      middlewareLogger.warn('User cannot afford action', {
        actionType,
        userId,
        actionCost,
      });

      return {
        success: false,
        error: `Insufficient $COPY balance. Need ${actionCost} $COPY for ${actionType}`,
        costDeducted: 0,
      };
    }

    // Step 3: Execute the handler
    middlewareLogger.info('Executing action handler', { actionType, userId, actionCost });

    let handlerResult: T;
    try {
      handlerResult = await handler();
    } catch (handlerError) {
      middlewareLogger.error('Handler execution failed', {
        actionType,
        userId,
        error: handlerError,
      });

      return {
        success: false,
        error: `Action failed: ${handlerError instanceof Error ? handlerError.message : 'Unknown error'}`,
        costDeducted: 0,
      };
    }

    // Step 4: Deduct tokens (only if handler succeeded)
    const transaction: UsageTransaction = {
      userId,
      actionType,
      costAmount: actionCost,
      metadata,
      relatedEntityId,
      description: description || `${actionType} execution`,
    };

    try {
      await TokenUsageService.deductTokensForAction(transaction);

      middlewareLogger.info('Token usage completed successfully', {
        actionType,
        userId,
        costDeducted: actionCost,
      });

      return {
        success: true,
        data: handlerResult,
        costDeducted: actionCost,
      };

    } catch (deductionError) {
      middlewareLogger.error('Failed to deduct tokens after successful action', {
        actionType,
        userId,
        actionCost,
        error: deductionError,
      });

      // This is a critical error - the action succeeded but we couldn't charge
      // In a production system, you might want to implement compensation logic
      return {
        success: false,
        error: `Action completed but billing failed: ${deductionError instanceof Error ? deductionError.message : 'Unknown error'}`,
        costDeducted: 0,
      };
    }

  } catch (error) {
    middlewareLogger.error('Critical error in token usage middleware', {
      actionType,
      userId,
      error,
    });

    return {
      success: false,
      error: `System error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      costDeducted: 0,
    };
  }
}

/**
 * Simplified wrapper for common use cases
 */
export async function executeWithTokens<T>(
  userId: string,
  actionType: TokenAction,
  handler: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  const result = await withTokenUsage(
    {
      actionType,
      userId,
      metadata,
      skipIfDisabled: true,
    },
    handler
  );

  if (!result.success) {
    throw new Error(result.error || 'Token usage failed');
  }

  return result.data!;
}

/**
 * Check if user can afford an action without executing it
 */
export async function checkActionAffordability(
  userId: string,
  actionType: TokenAction
): Promise<{ canAfford: boolean; cost: number; error?: string }> {
  try {
    const cost = await TokenUsageService.getActionCost(actionType);
    const canAfford = await TokenUsageService.canAffordAction(userId, actionType);

    return {
      canAfford,
      cost,
    };
  } catch (error) {
    middlewareLogger.error('Error checking affordability', {
      userId,
      actionType,
      error,
    });

    return {
      canAfford: false,
      cost: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get action cost without user context
 */
export async function getActionCost(actionType: TokenAction): Promise<number> {
  try {
    return await TokenUsageService.getActionCost(actionType);
  } catch (error) {
    middlewareLogger.error('Error getting action cost', { actionType, error });
    return 0;
  }
}
