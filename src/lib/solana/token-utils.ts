import {
  Connection,
  PublicKey,
  Transaction,
  SystemProgram,
  LAMPORTS_PER_SOL,
} from '@solana/web3.js';
import {
  TOKEN_PROGRAM_ID,
  getAssociatedTokenAddress,
  createAssociatedTokenAccountInstruction,
  createTransferInstruction,
  getAccount,
  TokenAccountNotFoundError,
  TokenInvalidAccountOwnerError,
} from '@solana/spl-token';
import { solanaConnection } from './connection';
import { CONTRACTS } from '@/lib/web3/config';
import { getTokenAddressForNetwork } from '@/contexts/network-context';

/**
 * SPL Token utilities for Solana
 */

/**
 * Get token balance for a wallet
 */
export async function getTokenBalance(
  walletPublicKey: PublicKey,
  mintAddress: string,
  connection?: Connection
): Promise<number> {
  const conn = connection || solanaConnection;
  const mint = new PublicKey(mintAddress);

  try {
    console.log('🪙 Fetching token balance:', {
      wallet: walletPublicKey.toString(),
      mint: mintAddress,
    });

    // Get associated token account address
    const associatedTokenAddress = await getAssociatedTokenAddress(
      mint,
      walletPublicKey
    );

    // Get token account info
    const tokenAccount = await getAccount(conn, associatedTokenAddress);
    const balance = Number(tokenAccount.amount);

    console.log('🪙 Token balance:', {
      wallet: walletPublicKey.toString(),
      mint: mintAddress,
      balance,
      associatedTokenAddress: associatedTokenAddress.toString(),
    });

    return balance;
  } catch (error) {
    if (error instanceof TokenAccountNotFoundError || error instanceof TokenInvalidAccountOwnerError) {
      console.log('🪙 Token account not found, balance is 0');
      return 0;
    }

    console.error('❌ Error fetching token balance:', error);
    throw new Error('Failed to fetch token balance');
  }
}

/**
 * Get $COPY token balance for a specific network
 */
export async function getCopyTokenBalance(
  walletPublicKey: PublicKey,
  connection?: Connection,
  network?: string
): Promise<number> {
  // If network is provided, use network-specific token address
  if (network) {
    const networkAddresses = getTokenAddressForNetwork(network as any);
    console.log('🪙 Using network-specific COPY token address:', {
      network,
      tokenAddress: networkAddresses.COPY_TOKEN,
      wallet: walletPublicKey.toString(),
    });
    return getTokenBalance(walletPublicKey, networkAddresses.COPY_TOKEN, connection);
  }

  // Fallback to default CONTRACTS.COPY_TOKEN
  console.log('🪙 Using default COPY token address:', {
    tokenAddress: CONTRACTS.COPY_TOKEN,
    wallet: walletPublicKey.toString(),
  });
  return getTokenBalance(walletPublicKey, CONTRACTS.COPY_TOKEN, connection);
}

/**
 * Create associated token account if it doesn't exist
 */
export async function createAssociatedTokenAccountIfNeeded(
  payer: PublicKey,
  owner: PublicKey,
  mintAddress: string,
  connection?: Connection
): Promise<{ instruction: ReturnType<typeof createAssociatedTokenAccountInstruction>; address: PublicKey } | null> {
  const conn = connection || solanaConnection;
  const mint = new PublicKey(mintAddress);

  try {
    const associatedTokenAddress = await getAssociatedTokenAddress(mint, owner);

    // Check if account already exists
    try {
      await getAccount(conn, associatedTokenAddress);
      console.log('🪙 Associated token account already exists:', associatedTokenAddress.toString());
      return null; // Account already exists
    } catch (error) {
      if (error instanceof TokenAccountNotFoundError) {
        // Account doesn't exist, create instruction
        console.log('🪙 Creating associated token account:', associatedTokenAddress.toString());

        const instruction = createAssociatedTokenAccountInstruction(
          payer,
          associatedTokenAddress,
          owner,
          mint
        );

        return {
          instruction,
          address: associatedTokenAddress,
        };
      }
      throw error;
    }
  } catch (error) {
    console.error('❌ Error creating associated token account:', error);
    throw new Error('Failed to create associated token account');
  }
}

/**
 * Transfer SPL tokens
 */
export async function transferTokens(
  fromWallet: PublicKey,
  toWallet: PublicKey,
  mintAddress: string,
  amount: number,
  connection?: Connection
): Promise<Transaction> {
  const conn = connection || solanaConnection;
  const mint = new PublicKey(mintAddress);

  try {
    console.log('🔄 Preparing token transfer:', {
      from: fromWallet.toString(),
      to: toWallet.toString(),
      mint: mintAddress,
      amount,
    });

    // Get associated token addresses
    const fromTokenAccount = await getAssociatedTokenAddress(mint, fromWallet);
    const toTokenAccount = await getAssociatedTokenAddress(mint, toWallet);

    const transaction = new Transaction();

    // Create destination token account if needed
    const createAccountInstruction = await createAssociatedTokenAccountIfNeeded(
      fromWallet,
      toWallet,
      mintAddress,
      connection
    );

    if (createAccountInstruction) {
      transaction.add(createAccountInstruction.instruction);
    }

    // Add transfer instruction
    const transferInstruction = createTransferInstruction(
      fromTokenAccount,
      toTokenAccount,
      fromWallet,
      amount
    );

    transaction.add(transferInstruction);

    // Get recent blockhash
    const { blockhash } = await conn.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = fromWallet;

    console.log('🔄 Token transfer transaction prepared');
    return transaction;
  } catch (error) {
    console.error('❌ Error preparing token transfer:', error);
    throw new Error('Failed to prepare token transfer');
  }
}

/**
 * Transfer $COPY tokens
 */
export async function transferCopyTokens(
  fromWallet: PublicKey,
  toWallet: PublicKey,
  amount: number,
  connection?: Connection
): Promise<Transaction> {
  return transferTokens(fromWallet, toWallet, CONTRACTS.COPY_TOKEN, amount, connection);
}

/**
 * Transfer SOL
 */
export async function transferSol(
  fromWallet: PublicKey,
  toWallet: PublicKey,
  amount: number,
  connection?: Connection
): Promise<Transaction> {
  const conn = connection || solanaConnection;

  try {
    console.log('💰 Preparing SOL transfer:', {
      from: fromWallet.toString(),
      to: toWallet.toString(),
      amount,
    });

    const transaction = new Transaction();

    const transferInstruction = SystemProgram.transfer({
      fromPubkey: fromWallet,
      toPubkey: toWallet,
      lamports: amount * LAMPORTS_PER_SOL,
    });

    transaction.add(transferInstruction);

    // Get recent blockhash
    const { blockhash } = await conn.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = fromWallet;

    console.log('💰 SOL transfer transaction prepared');
    return transaction;
  } catch (error) {
    console.error('❌ Error preparing SOL transfer:', error);
    throw new Error('Failed to prepare SOL transfer');
  }
}

/**
 * Get all token accounts for a wallet
 */
export async function getTokenAccounts(
  walletPublicKey: PublicKey,
  connection?: Connection
) {
  const conn = connection || solanaConnection;

  try {
    console.log('🪙 Fetching all token accounts for:', walletPublicKey.toString());

    const tokenAccounts = await conn.getParsedTokenAccountsByOwner(
      walletPublicKey,
      {
        programId: TOKEN_PROGRAM_ID,
      }
    );

    const accounts = tokenAccounts.value.map((account) => ({
      pubkey: account.pubkey.toString(),
      mint: account.account.data.parsed.info.mint,
      balance: account.account.data.parsed.info.tokenAmount.uiAmount,
      decimals: account.account.data.parsed.info.tokenAmount.decimals,
    }));

    console.log('🪙 Token accounts found:', accounts.length);
    return accounts;
  } catch (error) {
    console.error('❌ Error fetching token accounts:', error);
    throw new Error('Failed to fetch token accounts');
  }
}
